#!/usr/bin/env python3
"""
Optimize cropland & pasture allocation for crops and livestock.

Requires: pip install pulp
"""

from __future__ import annotations
import argparse
import math
from typing import Dict, Tuple
import sys

try:
    import pulp
except ImportError:
    print("This script requires PuLP. Install with: pip install pulp", file=sys.stderr)
    sys.exit(1)

# -------------------------
# Data Tables (constants)
# -------------------------

CROPS = {
    # name       HR   BaseYield(d/acre)  BaseLabor(hrs/acre)
    "Rye":        {"HR": 0,   "yield": 45.0, "labor": 5.0},
    "Barley":     {"HR": 10,  "yield": 48.0, "labor": 6.0},
    "Oats":       {"HR": 5,   "yield": 42.0, "labor": 5.0},
    "Hay":        {"HR": 10,  "yield": 40.0, "labor": 5.0},
    "Vegetables": {"HR": -10, "yield": 80.0, "labor": 8.0},
    "Flax":       {"HR": -20, "yield": 66.0, "labor": 6.0},
    "Wheat":      {"HR": -20, "yield": 72.0, "labor": 6.0},
    "Fruit":      {"HR": -30, "yield": 90.0, "labor": 7.0},
}

LIVESTOCK = {
    # name   HR   BaseYield(d/head)  BaseLabor(hrs/head)  AcresPerHead
    "Oxen":  {"HR": 0,   "yield": 130.0, "labor": 17.0, "acres": 2.0},
    "Cows":  {"HR": -5,  "yield": 188.0, "labor": 20.0, "acres": 2.0},
    "Goats": {"HR": 10,  "yield": 40.0,  "labor": 5.0,  "acres": 0.5},
    "Sheep": {"HR": -10, "yield": 33.0,  "labor": 3.0,  "acres": 1.0/3.0},
    "Swine": {"HR": 5,   "yield": 15.0,  "labor": 2.0,  "acres": 0.1},
}

REGION_HR_MOD = {
    "Orbaal":            -10,
    "Azadmere":          -10,
    "Peran":             -10,
    "Tharda":            -5,
    "Kaldor":            0,
    "Rethem":            0,
    "Chybisa":           0,
    "Kanday":            5,
    "Evael":             5,
    "Melderyn-mainland": 5,
    "Melderyn-island":   10,
}

# Regions where oxen minimum is total_crop_acres / 5 (else /10)
OXEN_STRICT_REGIONS = {"Orbaal", "Azadmere", "Peran"}


# -------------------------
# Helper functions
# -------------------------

def crop_yield_per_acre(crop: str, region: str, lq: float, fief: float, weather: float) -> float:
    """Compute yield per acre (drams) for a crop."""
    base = CROPS[crop]["yield"]
    hr = CROPS[crop]["HR"] + REGION_HR_MOD.get(region, 0)
    factor = weather + (hr / 100.0)  # per spec
    return base * lq * fief * factor


def crop_labor_per_acre(crop: str) -> float:
    return CROPS[crop]["labor"]


def livestock_yield_per_head(kind: str, lq: float, fief: float, weather: float) -> float:
    """Compute yield per head (drams) for a livestock type."""
    base = LIVESTOCK[kind]["yield"]
    hr = LIVESTOCK[kind]["HR"]  # region modifier does NOT apply to livestock
    factor = weather + (hr / 100.0)
    return base * lq * fief * factor


def livestock_labor_per_head(kind: str) -> float:
    return LIVESTOCK[kind]["labor"]


def livestock_acres_per_head(kind: str) -> float:
    return LIVESTOCK[kind]["acres"]


# -------------------------
# Optimization
# -------------------------

def optimize(
    region: str,
    crop_acres: float,
    pasture_acres: float,
    labor_total: float,
    lq: float,
    fief: float,
    weather: float,
    crop_max_share: float,
    livestock_max_share: float,
    crop_min_share: float,
    livestock_min_share: float,
    excluded_crops: set,
    excluded_livestock: set,
) -> Tuple[pulp.LpStatusOptimal | str, Dict[str, float], Dict[str, float]]:
    """
    Returns:
      (status, crop_acres_alloc: dict, livestock_heads_alloc: dict)
    """
    prob = pulp.LpProblem("Farm_Allocation", pulp.LpMaximize)

    # Decision variables
    crop_vars = {
        name: pulp.LpVariable(f"acres_{name}", lowBound=0, cat=pulp.LpContinuous)
        for name in CROPS
    }
    head_vars = {
        name: pulp.LpVariable(f"heads_{name}", lowBound=0, cat=pulp.LpInteger)
        for name in LIVESTOCK
    }

    # Totals
    total_crop_acres_used = pulp.lpSum(crop_vars.values())
    total_pasture_acres_used = pulp.lpSum(
        head_vars[k] * livestock_acres_per_head(k) for k in LIVESTOCK
    )
    total_labor_used = (
        pulp.lpSum(crop_vars[c] * crop_labor_per_acre(c) for c in CROPS)
        + pulp.lpSum(head_vars[k] * livestock_labor_per_head(k) for k in LIVESTOCK)
    )

    # Objective: maximize revenue
    crop_revenue = pulp.lpSum(
        crop_vars[c] * crop_yield_per_acre(c, region, lq, fief, weather) for c in CROPS
    )
    livestock_revenue = pulp.lpSum(
        head_vars[k] * livestock_yield_per_head(k, lq, fief, weather) for k in LIVESTOCK
    )
    prob += crop_revenue + livestock_revenue

    # Constraints
    prob += total_crop_acres_used <= crop_acres, "Crop_Acres_Limit"
    prob += total_pasture_acres_used <= pasture_acres, "Pasture_Acres_Limit"
    prob += total_labor_used <= labor_total, "Labor_Limit"

    # Minimum oxen requirement (region-based)
    oxen_min = (crop_acres / 5.0) if region in OXEN_STRICT_REGIONS else (crop_acres / 10.0)
    # Oxen are counted in HEADS, must be >= ceil of requirement
    oxen_required = math.ceil(oxen_min)
    prob += head_vars["Oxen"] >= oxen_required, "Minimum_Oxen"

    # Maximum oxen constraint (1.5x minimum requirement)
    oxen_max = 1.5 * oxen_min
    oxen_max_heads = math.floor(oxen_max)
    prob += head_vars["Oxen"] <= oxen_max_heads, "Maximum_Oxen"

    # Diversification caps within category (linear; guards against over-concentration)
    for c in CROPS:
        if c != "Hay" and c not in excluded_crops:  # Exclude hay and excluded crops from diversification constraints
            prob += crop_vars[c] <= crop_max_share * crop_acres, f"Diversity_Crop_Max_{c}"
    for k in LIVESTOCK:
        if k != "Oxen" and k not in excluded_livestock:  # Exclude oxen and excluded livestock from diversification constraints
            prob += head_vars[k] * livestock_acres_per_head(k) <= livestock_max_share * pasture_acres, f"Diversity_Livestock_Max_{k}"

    # Minimum diversification requirements (ensures some allocation to each type)
    for c in CROPS:
        if c != "Hay" and c not in excluded_crops:  # Exclude hay and excluded crops from diversification constraints
            prob += crop_vars[c] >= crop_min_share * crop_acres, f"Diversity_Crop_Min_{c}"
    for k in LIVESTOCK:
        if k != "Oxen" and k not in excluded_livestock:  # Exclude oxen and excluded livestock from diversification constraints
            prob += head_vars[k] * livestock_acres_per_head(k) >= livestock_min_share * pasture_acres, f"Diversity_Livestock_Min_{k}"

    # Hay requirement based on livestock needs
    # Hay revenue must be at least: [Total Livestock Acres] * 12d * 0.75
    hay_revenue_required = total_pasture_acres_used * 12.0 * 0.75
    hay_revenue_actual = crop_vars["Hay"] * crop_yield_per_acre("Hay", region, lq, fief, weather)
    prob += hay_revenue_actual >= hay_revenue_required, "Hay_Livestock_Requirement"

    # Exclusion constraints (set excluded crops/livestock to 0)
    for crop in excluded_crops:
        if crop in crop_vars:
            prob += crop_vars[crop] == 0, f"Exclude_Crop_{crop}"

    for livestock in excluded_livestock:
        if livestock in head_vars:
            prob += head_vars[livestock] == 0, f"Exclude_Livestock_{livestock}"

    # Solve
    solver = pulp.PULP_CBC_CMD(msg=False)
    result_status = prob.solve(solver)

    status = pulp.LpStatus[result_status]
    crop_alloc = {c: pulp.value(var) or 0.0 for c, var in crop_vars.items()}
    livestock_alloc = {k: pulp.value(var) or 0.0 for k, var in head_vars.items()}

    return status, crop_alloc, livestock_alloc


# -------------------------
# Reporting
# -------------------------

def summarize(
    region: str,
    crop_acres: float,
    pasture_acres: float,
    labor_total: float,
    lq: float,
    fief: float,
    weather: float,
    crop_alloc: Dict[str, float],
    livestock_alloc: Dict[str, float],
):
    # Column widths
    def fmt_d(v: float) -> str:
        return f"{v:,.2f}d"

    def fmt_revenue(v: float) -> str:
        """Format revenue in drams and farthings (1d = 4f)"""
        # Round to nearest farthing (quarter dram)
        total_farthings = round(v * 4)
        drams = total_farthings // 4
        farthings = total_farthings % 4

        if farthings == 0:
            return f"{drams:,}d"
        else:
            return f"{drams:,}d {farthings}f"

    def fmt_num(v: float) -> str:
        return f"{v:,.2f}"

    # CROP TABLE
    print("\nCROPS")
    print("-" * 82)
    print(f"{'Crop':<14}{'Acres':>10}{'%':>8}{'Yield/acre':>14}{'Labor/acre':>14}{'Revenue':>14}")
    print("-" * 82)
    total_crop_acres = 0.0
    total_crop_labor = 0.0
    total_crop_revenue = 0.0
    for c in CROPS:
        acres = crop_alloc.get(c, 0.0)
        pct = (acres / crop_acres * 100) if crop_acres > 0 else 0.0
        ypa = crop_yield_per_acre(c, region, lq, fief, weather)
        lpa = crop_labor_per_acre(c)
        rev = acres * ypa
        lab = acres * lpa
        total_crop_acres += acres
        total_crop_labor += lab
        total_crop_revenue += rev
        print(f"{c:<14}{fmt_num(acres):>10}{pct:>7.1f}%{fmt_d(ypa):>14}{fmt_num(lpa):>14}{fmt_revenue(rev):>14}")
    print("-" * 82)
    total_crop_pct = (total_crop_acres / crop_acres * 100) if crop_acres > 0 else 0.0
    print(f"{'TOTAL':<14}{fmt_num(total_crop_acres):>10}{total_crop_pct:>7.1f}%{'':>14}{fmt_num(total_crop_labor):>14}{fmt_revenue(total_crop_revenue):>14}")

    # LIVESTOCK TABLE
    print("\nLIVESTOCK")
    print("-" * 84)
    print(f"{'Type':<14}{'Head':>10}{'%':>8}{'Yld/head':>12}{'Labor/head':>14}{'Pasture ac':>12}{'Revenue':>14}")
    print("-" * 84)
    total_heads = 0.0
    total_pasture_used = 0.0
    total_livestock_labor = 0.0
    total_livestock_revenue = 0.0
    for k in LIVESTOCK:
        heads = livestock_alloc.get(k, 0.0)
        heads = round(heads)  # Always round to integer heads
        acs = heads * livestock_acres_per_head(k)
        pct = (acs / pasture_acres * 100) if pasture_acres > 0 else 0.0
        yph = livestock_yield_per_head(k, lq, fief, weather)
        lph = livestock_labor_per_head(k)
        aph = livestock_acres_per_head(k)
        rev = heads * yph
        lab = heads * lph
        total_heads += heads
        total_pasture_used += acs
        total_livestock_labor += lab
        total_livestock_revenue += rev
        print(f"{k:<14}{fmt_num(heads):>10}{pct:>7.1f}%{fmt_d(yph):>12}{fmt_num(lph):>14}{fmt_num(acs):>12}{fmt_revenue(rev):>14}")
    print("-" * 84)
    total_pasture_pct = (total_pasture_used / pasture_acres * 100) if pasture_acres > 0 else 0.0
    print(f"{'TOTAL':<14}{fmt_num(total_heads):>10}{total_pasture_pct:>7.1f}%{'':>12}{fmt_num(total_livestock_labor):>14}{fmt_num(total_pasture_used):>12}{fmt_revenue(total_livestock_revenue):>14}")

    # GRAND TOTALS & UTILIZATION
    grand_labor = total_crop_labor + total_livestock_labor
    grand_revenue = total_crop_revenue + total_livestock_revenue
    print("\nSUMMARY")
    print("-" * 40)
    print(f"{'Crop acres available:':<24} {fmt_num(crop_acres)}")
    print(f"{'Crop acres used:':<24} {fmt_num(total_crop_acres)}")
    print(f"{'Pasture acres available:':<24} {fmt_num(pasture_acres)}")
    print(f"{'Pasture acres used:':<24} {fmt_num(total_pasture_used)}")
    print(f"{'Labor available:':<24} {fmt_num(labor_total)}")
    print(f"{'Labor used:':<24} {fmt_num(grand_labor)}")
    print(f"{'TOTAL REVENUE:':<24} {fmt_revenue(grand_revenue)}")
    print("-" * 40)
    print()


# -------------------------
# CLI
# -------------------------

def positive_float(name: str):
    def _cast(x: str) -> float:
        v = float(x)
        if v < 0:
            raise argparse.ArgumentTypeError(f"{name} must be non-negative")
        return v
    return _cast

def main():
    p = argparse.ArgumentParser(description="Optimize cropland & pasture allocation.")
    p.add_argument("--region", required=True, choices=sorted(REGION_HR_MOD.keys()), help="Region name (affects crop HR only)")
    p.add_argument("--crop-acres", type=positive_float("crop-acres"), required=True, help="Total cropland acres")
    p.add_argument("--pasture-acres", type=positive_float("pasture-acres"), required=True, help="Total pasture acres")
    p.add_argument("--labor", type=positive_float("labor"), required=True, help="Total available labor (hours)")
    p.add_argument("--lq", type=positive_float("lq"), required=True, help="Land Quality Index (e.g., 1.00)")
    p.add_argument("--fief", type=positive_float("fief"), required=True, help="Fief Index (e.g., 1.00)")
    p.add_argument("--weather", type=positive_float("weather"), required=True, help="Weather Index (e.g., 1.00)")
    p.add_argument("--crop-max-share", type=float, default=0.33, help="Max share of total crop acres any single crop can occupy (0-1, default 0.33)")
    p.add_argument("--crop-min-share", type=float, default=0.0, help="Min share of total crop acres each crop must occupy (0-1, default 0.0)")
    p.add_argument("--livestock-max-share", type=float, default=0.33, help="Max share of total pasture acres any single livestock type can occupy (0-1, default 0.33)")
    p.add_argument("--livestock-min-share", type=float, default=0.0, help="Min share of total pasture acres each livestock type must occupy (0-1, default 0.0)")

    # Crop exclusion flags
    for crop in CROPS:
        flag_name = f"--no-{crop.lower()}"
        help_text = f"Exclude {crop} from cultivation (set acres to 0)"
        p.add_argument(flag_name, action="store_true", help=help_text)

    # Livestock exclusion flags
    for livestock in LIVESTOCK:
        flag_name = f"--no-{livestock.lower()}"
        help_text = f"Exclude {livestock} from the settlement (set heads to 0)"
        p.add_argument(flag_name, action="store_true", help=help_text)
    args = p.parse_args()

    if not (0.0 < args.crop_max_share <= 1.0):
        p.error("--crop-max-share must be in [0, 1]")
    if not (0.0 < args.livestock_max_share <= 1.0):
        p.error("--livestock-max-share must be in [0, 1]")
    if not (0.0 <= args.crop_min_share < 1.0):
        p.error("--crop-min-share must be in [0, 1]")
    if not (0.0 <= args.livestock_min_share < 1.0):
        p.error("--livestock-min-share must be in [0, 1]")
    if args.crop_min_share >= args.crop_max_share:
        p.error("--crop-min-share must be less than --crop-max-share")
    if args.livestock_min_share >= args.livestock_max_share:
        p.error("--livestock-min-share must be less than --livestock-max-share")

    # Collect excluded crops and livestock from command line flags
    excluded_crops = set()
    for crop in CROPS:
        flag_attr = f"no_{crop.lower()}"
        if hasattr(args, flag_attr) and getattr(args, flag_attr):
            excluded_crops.add(crop)

    excluded_livestock = set()
    for livestock in LIVESTOCK:
        flag_attr = f"no_{livestock.lower()}"
        if hasattr(args, flag_attr) and getattr(args, flag_attr):
            excluded_livestock.add(livestock)

    status, crop_alloc, livestock_alloc = optimize(
        region=args.region,
        crop_acres=args.crop_acres,
        pasture_acres=args.pasture_acres,
        labor_total=args.labor,
        lq=args.lq,
        fief=args.fief,
        weather=args.weather,
        crop_max_share=args.crop_max_share,
        livestock_max_share=args.livestock_max_share,
        crop_min_share=args.crop_min_share,
        livestock_min_share=args.livestock_min_share,
        excluded_crops=excluded_crops,
        excluded_livestock=excluded_livestock,
    )

    if status != "Optimal":
        print(f"Optimization status: {status}")
        # Quick hints for common infeasibility causes
        # 1) Not enough pasture to meet minimum oxen requirement
        denom = 5.0 if args.region in OXEN_STRICT_REGIONS else 10.0
        oxen_min = math.ceil(args.crop_acres / denom)
        oxen_pasture_needed = oxen_min * LIVESTOCK["Oxen"]["acres"]
        if oxen_pasture_needed > args.pasture_acres + 1e-9:
            print(f"- Minimum oxen requirement implies at least {oxen_pasture_needed:.2f} pasture acres for oxen alone; you only have {args.pasture_acres:.2f}.")
        print("- Try increasing pasture acres or total labor.")
        print("- You can also relax diversification caps: --crop-max-share / --livestock-max-share / --crop-min-share / --livestock-min-share.")
        sys.exit(2)

    summarize(
        region=args.region,
        crop_acres=args.crop_acres,
        pasture_acres=args.pasture_acres,
        labor_total=args.labor,
        lq=args.lq,
        fief=args.fief,
        weather=args.weather,
        crop_alloc=crop_alloc,
        livestock_alloc=livestock_alloc,
    )


if __name__ == "__main__":
    main()

